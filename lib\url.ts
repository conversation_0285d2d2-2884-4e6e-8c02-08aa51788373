import type { NextRequest } from 'next/server'

export function getPublicBaseUrl(req?: NextRequest) {
  const fromEnv = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_APP_URL
  if (fromEnv) return fromEnv.replace(/\/$/, '')

  if (req) {
    const proto = req.headers.get('x-forwarded-proto') || 'https'
    const host = req.headers.get('x-forwarded-host') || req.headers.get('host')
    if (host) return `${proto}://${host}`
  }

  if (process.env.NODE_ENV !== 'production') {
    return 'http://localhost:3001'
  }

  return 'https://moncoyfinance.com'
}
