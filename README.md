# Moncoy Finance Landing Page

Landing page com integração Stripe para o Moncoy Finance.

## Configuração do Stripe

1. **Obtenha suas chaves do Stripe:**
   - Acesse [Stripe Dashboard](https://dashboard.stripe.com)
   - Vá em Developers → API keys
   - Copie a chave pública (pk_test_...)

2. **Configure os preços:**
   - No Stripe Dashboard, vá em Products
   - Crie 3 produtos:
     - Básico: R$ 19,90/mês
     - Pro: R$ 49,90/mês  
     - Premium: R$ 59,90/mês
   - Copie os IDs dos preços (price_...)

3. **Atualize o arquivo `src/stripe.ts`:**
   ```typescript
   const stripePromise = loadStripe('');
   
   export const STRIPE_PRICES = {
     BASICO: 'price_1RtvdYLhhuHU7ecWyxiz6zti'
'    PRO: 'price_1Rx7OBLhhuHU7ecWhotGOhi7',
,    PREMIUM: "price_1Rx7ZsLhhuHU7ecWtA3wXXXI"

   };
   ```

## Instalação

```bash
pnpm install
```

## Desenvolvimento

```bash
pnpm dev
```

## Funcionalidades

- ✅ Landing page completa
- ✅ Integração com Stripe Checkout
- ✅ 3 planos de assinatura
- ✅ Página de sucesso
- ✅ Redirecionamento para dashboard

## URLs de Redirecionamento

Configure no Stripe Dashboard:
- Success URL: `http://localhost:3001/success`
- Cancel URL: `http://localhost:3001/cancel`