import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Moncoy Finance - Transforme Suas Finanças com Inteligência Artificial',
  description: 'O primeiro app brasileiro que combina gestão financeira completa com IA nativa. Organize, invista e cresça com insights inteligentes personalizados para você.',
  openGraph: {
    title: 'Moncoy Finance - Transforme Suas Finanças com Inteligência Artificial',
    description: 'O primeiro app brasileiro que combina gestão financeira completa com IA nativa. Organize, invista e cresça com insights inteligentes personalizados para você.',
    url: 'https://www.moncoyfinance.com.br', // Replace with your actual domain
    siteName: 'Moncoy Finance',
    images: [
      {
        url: 'https://www.moncoyfinance.com.br/og-image.jpg', // Replace with your actual OG image URL
        width: 1200,
        height: 630,
        alt: 'Moncoy Finance - Inteligência Financeira com IA',
      },
    ],
    locale: 'pt_BR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Moncoy Finance - Transforme Suas Finanças com Inteligência Artificial',
    description: 'O primeiro app brasileiro que combina gestão financeira completa com IA nativa. Organize, invista e cresça com insights inteligentes personalizados para você.',
    creator: '@moncoyfinance', // Replace with your Twitter handle
    images: ['https://www.moncoyfinance.com.br/twitter-image.jpg'], // Replace with your actual Twitter image URL
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pt-BR">
      <body>{children}</body>
    </html>
  )
}