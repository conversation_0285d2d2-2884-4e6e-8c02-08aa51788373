"use client";

import { useEffect, useState } from "react";
import { redirectToCheckout } from "../lib/stripe";

// FAQ Component
function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm fade-in">
      <button
        className="w-full text-left p-6 focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex justify-between items-center">
          <h3 className="font-semibold text-lg text-gray-900">{question}</h3>
          <span className="text-gray-400">{isOpen ? "−" : "+"}</span>
        </div>
      </button>
      {isOpen && (
        <div className="p-6 pt-0">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function LandingPage() {
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("visible");
        }
      });
    }, observerOptions);

    document.querySelectorAll(".fade-in").forEach((el) => {
      observer.observe(el);
    });

    // Smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
      anchor.addEventListener("click", (e) => {
        e.preventDefault();
        const href = (e.currentTarget as HTMLAnchorElement).getAttribute(
          "href"
        );
        if (href && href !== "#") {
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }
      });
    });

    return () => observer.disconnect();
  }, []);

  return (
    <div className="bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm fixed w-full top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">M</span>
              </div>
              <span className="ml-3 text-2xl font-bold text-gray-900">
                Moncoy Finance
              </span>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a
                href="#recursos"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Recursos
              </a>
              <a
                href="#planos"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Planos
              </a>
              <a
                href="#faq"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                FAQ
              </a>
            </nav>
            <button className="gradient-bg text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg transition-all pulse-glow">
              Comece com 30 dias Grátis
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-24 pb-20 gradient-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-white fade-in">
              <h1 className="text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Transforme Suas Finanças com
                <span className="block text-yellow-300">
                  Inteligência Artificial
                </span>
              </h1>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                O primeiro app brasileiro que combina gestão financeira completa
                com IA nativa. Organize, invista e cresça com insights
                inteligentes personalizados para você.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-50 transition-all shadow-xl pulse-glow">
                  🚀 Comece Grátis Agora
                </button>
                <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all">
                  📱 Ver Demo
                </button>
              </div>
              <div className="mt-8 flex items-center text-blue-100">
                <span className="text-sm">✅ Básico</span>
                <span className="mx-4">•</span>
                <span className="text-sm">✅ IA liberada em 22 dias</span>
                <span className="mx-4">•</span>
        
              </div>
            </div>
            <div className="fade-in">
              <div className="w-full h-96 bg-white bg-opacity-20 rounded-2xl shadow-2xl flex items-center justify-center">
                <span className="text-white text-lg">Dashboard Preview</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problema */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 fade-in">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Você Está Cansado de{" "}
              <span className="gradient-text">
                Não Saber Para Onde Vai Seu Dinheiro?
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Sabemos como é frustrante tentar organizar as finanças sem as
              ferramentas certas.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6 fade-in">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">😰</span>
              </div>
              <h3 className="font-semibold text-lg mb-3 text-gray-900">
                Desorganização Total
              </h3>
              <p className="text-gray-600">
                Planilhas confusas, apps limitados e zero visibilidade real dos
                seus gastos
              </p>
            </div>
            <div className="text-center p-6 fade-in">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">📉</span>
              </div>
              <h3 className="font-semibold text-lg mb-3 text-gray-900">
                Metas Impossíveis
              </h3>
              <p className="text-gray-600">
                Objetivos financeiros que nunca saem do papel por falta de
                planejamento
              </p>
            </div>
            <div className="text-center p-6 fade-in">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🤯</span>
              </div>
              <h3 className="font-semibold text-lg mb-3 text-gray-900">
                Decisões no Escuro
              </h3>
              <p className="text-gray-600">
                Investir ou poupar? Sem dados claros, toda decisão vira um tiro
                no escuro
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Recursos */}
      <section id="recursos" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 fade-in">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Recursos <span className="gradient-text">Profissionais</span> ao
              Seu Alcance
            </h2>
            <p className="text-xl text-gray-600">
              Tudo que você precisa para transformar sua vida financeira
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "📊",
                title: "Gestão Completa",
                desc: "Dashboard 360° com controle total de receitas, despesas e múltiplas contas",
              },
              {
                icon: "🚀",
                title: "Metas Inteligentes",
                desc: "Defina objetivos e acompanhe progresso com sistema de priorização visual",
              },
              {
                icon: "📈",
                title: "Investimentos Pro",
                desc: "Portfólio completo com ações, fundos e criptomoedas em um só lugar",
              },
              {
                icon: "🔒",
                title: "Segurança Premium",
                desc: "Criptografia bancária e proteção de dados no mesmo nível dos grandes bancos",
              },
            ].map((item, i) => (
              <div
                key={i}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow fade-in"
              >
                <div className="w-14 h-14 gradient-bg rounded-lg flex items-center justify-center mb-4">
                  <span className="text-white text-2xl">{item.icon}</span>
                </div>
                <h3 className="font-bold text-lg mb-3 text-gray-900">
                  {item.title}
                </h3>
                <p className="text-gray-600">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Planos */}
      <section id="planos" className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 fade-in">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Escolha o Plano <span className="gradient-text">Perfeito</span>{" "}
              Para Você
            </h2>
            <p className="text-xl text-gray-600">
              Escolha o plano ideal para suas necessidades
            </p>
          </div>
          <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Plano Básico */}
            <div className="bg-white p-8 rounded-2xl shadow-lg fade-in">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Básico
                </h3>
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  R$ 19,90
                </div>
                <p className="text-gray-600">por mês</p>
              </div>
              <ul className="space-y-4 mb-8">
                {[
                  "Funcionalidades essenciais",
                  "GPT-4o-mini",
                  "5 perguntas/semana",
                  "Resumo mensal simplificado",
                  "Somente Web",
                ].map((feature, i) => (
                  <li key={i} className="flex items-center">
                    <span className="text-green-500 mr-3">✅</span>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
              <button 
                onClick={() => redirectToCheckout('BASICO')}
                className="w-full border-2 border-blue-600 text-blue-600 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all"
              >
                Assinar Básico
              </button>
            </div>

            {/* Plano Pro */}
            <div className="bg-white p-8 rounded-2xl shadow-xl border-2 border-blue-500 relative fade-in">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  Mais Popular
                </span>
              </div>
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Pro
                </h3>
                <div className="text-4xl font-bold gradient-text mb-2">
                  R$ 49,90
                </div>
                <p className="text-gray-600">por mês</p>
              </div>
              <ul className="space-y-4 mb-8">
                {[
                  { text: "IA e recursos avançados", icon: "✅" },
                  { text: "GPT-4o-mini + GPT-4o limitado", icon: "🤖", bold: true },
                  { text: "1 pergunta/dia", icon: "📊" },
                  { text: "Resumo mensal detalhado em PDF", icon: "📄" },
                  { text: "Histórico de conversas", icon: "💬" },
                  { text: "Alertas de gastos", icon: "⚡" },
                  { text: "Web + Mobile", icon: "📱" },
                ].map((feature, i) => (
                  <li key={i} className="flex items-center">
                    <span className="text-blue-500 mr-3">{feature.icon}</span>
                    <span
                      className={`text-gray-700 ${
                        feature.bold ? "font-semibold" : ""
                      }`}
                    >
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>
              <button 
                onClick={() => redirectToCheckout('PRO')}
                className="w-full gradient-bg text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all pulse-glow"
              >
                Fazer Upgrade
              </button>
            </div>

            {/* Plano Premium */}
            <div className="bg-white p-8 rounded-2xl shadow-lg fade-in">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Premium
                </h3>
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  R$ 59,90
                </div>
                <p className="text-gray-600">por mês</p>
              </div>
              <ul className="space-y-4 mb-8">
                {[
                  "Recursos completos",
                  "GPT-4o-mini + GPT-4o completo",
                  "Uso diário sem limite rígido",
                  "Resumo mensal + análise financeira completa",
                  "Relatórios PDF avançados com gráficos",
                  "Histórico de conversas",
                  "Suporte prioritário",
                  "Web + Mobile",
                ].map((feature, i) => (
                  <li key={i} className="flex items-center">
                    <span className="text-green-500 mr-3">✅</span>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
              <button 
                onClick={() => redirectToCheckout('PREMIUM')}
                className="w-full border-2 border-gray-600 text-gray-600 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-all"
              >
                Fazer Upgrade
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Prova Social */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 fade-in">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              O Que Nossos Usuários{" "}
              <span className="gradient-text">Estão Falando</span>
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Marina Santos",
                role: "Analista de Marketing",
                content:
                  "Finalmente consegui organizar minhas finanças! A IA do Moncoy me ajudou a identificar gastos desnecessários e já estou economizando R$ 800 por mês.",
              },
              {
                name: "Carlos Oliveira",
                role: "Empreendedor",
                content:
                  "Como freelancer, sempre tive dificuldade para controlar as finanças. O Moncoy mudou isso completamente. Agora tenho uma visão clara de tudo!",
              },
              {
                name: "Rafaela Costa",
                role: "Desenvolvedora",
                content:
                  "Interface incrível e funcionalidades profissionais. O melhor de tudo é que o plano gratuito já resolve 90% das minhas necessidades. Recomendo!",
              },
            ].map((testimonial, i) => (
              <div key={i} className="bg-gray-50 p-6 rounded-xl fade-in">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full gradient-bg flex items-center justify-center mr-4">
                    <span className="text-white font-bold">
                      {testimonial.name[0]}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
                <div className="flex mb-3">
                  <span className="text-yellow-400">⭐⭐⭐⭐⭐</span>
                </div>
                <p className="text-gray-700">"{testimonial.content.replace('plano gratuito', 'plano básico')}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section id="faq" className="py-20 bg-blue-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 fade-in">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Perguntas <span className="gradient-text">Frequentes</span>
            </h2>
          </div>
          <div className="space-y-4">
            <FAQItem
              question="Como funciona o período de 22 dias para liberar a IA?"
              answer="Durante os primeiros 22 dias, nossa IA aprende seus hábitos financeiros e padrões de consumo. Após esse período, ela é liberada automaticamente com insights personalizados e conselhos específicos para seu perfil."
            />
            <FAQItem
              question="Meus dados estão seguros?"
              answer="Sim! Utilizamos criptografia de ponta a ponta e Row Level Security. Seus dados são protegidos com o mesmo nível de segurança dos principais bancos digitais do Brasil."
            />
            <FAQItem
              question="Posso cancelar a qualquer momento?"
              answer="Claro! Não há fidelidade. Você pode cancelar sua assinatura do plano Profissional a qualquer momento e continuar usando o plano Básico gratuitamente."
            />
            <FAQItem
              question="Quando a integração bancária estará disponível?"
              answer="Estamos trabalhando para lançar a integração bancária nos próximos meses. Usuários do plano Profissional terão acesso prioritário quando for liberada."
            />
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-20 gradient-bg">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="text-white fade-in">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Pare de Adiar Seu{" "}
              <span className="text-yellow-300">Sucesso Financeiro</span>
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Milhares de brasileiros já transformaram suas vidas financeiras
              com o Moncoy. Não fique para trás - comece sua jornada hoje mesmo!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-50 transition-all shadow-xl pulse-glow">
                🚀 Transforme Suas Finanças Hoje
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all">
                💡 Falar com Especialista
              </button>
            </div>
            <div className="text-sm text-blue-100">
              ✅ Planos a partir de R$ 19,90 • ✅ Sem compromisso • ✅ Suporte em
              português
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">M</span>
                </div>
                <h2 className="text-lg font-semibold">
                  MONCOY
                  <span className="text-amber-600 font-thin shadow-400 ">
                    Finance
                  </span>
                </h2>
              </div>
              <p className="text-gray-400">
                Transformando vidas através da inteligência financeira.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Produto</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Recursos
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Planos
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Segurança
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Suporte</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Central de Ajuda
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Contato
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Status
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Privacidade
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Termos
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Cookies
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Moncoy Finance. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
