import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { getPublicBaseUrl } from '@/lib/url'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function POST(req: NextRequest) {
  try {
    if (!process.env.STRIPE_SECRET_KEY || !process.env.STRIPE_SECRET_KEY.startsWith('sk_')) {
      return NextResponse.json({ error: 'STRIPE_SECRET_KEY ausente ou inválida no servidor.' }, { status: 500 })
    }
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

    const { priceId, plan } = await req.json().catch(() => ({}))
    if (!priceId || typeof priceId !== 'string') {
      return NextResponse.json({ error: 'priceId é obrigatório.' }, { status: 400 })
    }

  const origin = getPublicBaseUrl(req)

    const params: Stripe.Checkout.SessionCreateParams = {
      mode: 'subscription',
      line_items: [{ price: priceId, quantity: 1 }],
      success_url: `${origin}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/cancel`,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
    }

    if (typeof plan === 'string') {
      params.metadata = { plan }
    }

    const session = await stripe.checkout.sessions.create(params)

    return NextResponse.json({ sessionId: session.id })
  } catch (err: any) {
    console.error('Erro ao criar sessão do Stripe (landing page):', err)
    return NextResponse.json({ error: err.message || 'Erro interno' }, { status: 500 })
  }
}
