{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "es2017", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "lib/supabase.js", "next-env.d.ts", "./.next/types/**/*.ts"], "exclude": ["node_modules"]}