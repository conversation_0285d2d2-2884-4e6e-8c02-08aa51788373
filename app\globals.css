@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* MODO CLARO - Cores claras e vibrantes */
  --background: oklch(0.98 0.01 240); /* Branco azulado muito claro */
  --foreground: oklch(0.15 0.05 240); /* Azul escuro para texto */
  --card: oklch(0.95 0.02 240); /* Branco com leve tom azul */
  --card-foreground: oklch(0.15 0.05 240);
  --popover: oklch(0.95 0.02 240);
  --popover-foreground: oklch(0.15 0.05 240);
  --primary: oklch(0.55 0.18 240); /* Azul vibrante */
  --primary-foreground: oklch(0.98 0.01 240);
  --secondary: oklch(0.9 0.03 240); /* Cinza azulado claro */
  --secondary-foreground: oklch(0.25 0.05 240);
  --muted: oklch(0.92 0.02 240);
  --muted-foreground: oklch(0.45 0.03 240);
  --accent: oklch(0.88 0.04 240);
  --accent-foreground: oklch(0.25 0.05 240);
  --destructive: oklch(0.6 0.25 15); /* Vermelho claro */
  --destructive-foreground: oklch(0.98 0.01 240);
  --border: oklch(0.85 0.03 240);
  --input: oklch(0.95 0.02 240);
  --ring: oklch(0.55 0.18 240);
  --chart-1: oklch(0.6 0.2 30);
  --chart-2: oklch(0.55 0.15 180);
  --chart-3: oklch(0.5 0.12 270);
  --chart-4: oklch(0.65 0.18 120);
  --chart-5: oklch(0.7 0.16 60);
  --radius: 0.625rem;
  --sidebar: oklch(0.96 0.02 240);
  --sidebar-foreground: oklch(0.15 0.05 240);
  --sidebar-primary: oklch(0.55 0.18 240);
  --sidebar-primary-foreground: oklch(0.98 0.01 240);
  --sidebar-accent: oklch(0.9 0.03 240);
  --sidebar-accent-foreground: oklch(0.25 0.05 240);
  --sidebar-border: oklch(0.85 0.03 240);
  --sidebar-ring: oklch(0.55 0.18 240);
}

.dark {
  /* MODO ESCURO - Cores escuras profundas */
  --background: oklch(0.08 0.06 240); /* Azul muito escuro */
  --foreground: oklch(0.95 0.01 240); /* Branco azulado */
  --card: oklch(0.12 0.05 240); /* Azul escuro para cards */
  --card-foreground: oklch(0.95 0.01 240);
  --popover: oklch(0.12 0.05 240);
  --popover-foreground: oklch(0.95 0.01 240);
  --primary: oklch(0.65 0.2 240); /* Azul brilhante no escuro */
  --primary-foreground: oklch(0.08 0.06 240);
  --secondary: oklch(0.18 0.04 240); /* Cinza azulado escuro */
  --secondary-foreground: oklch(0.9 0.01 240);
  --muted: oklch(0.15 0.03 240);
  --muted-foreground: oklch(0.65 0.02 240);
  --accent: oklch(0.2 0.04 240);
  --accent-foreground: oklch(0.9 0.01 240);
  --destructive: oklch(0.5 0.2 15); /* Vermelho escuro */
  --destructive-foreground: oklch(0.95 0.01 240);
  --border: oklch(0.2 0.03 240);
  --input: oklch(0.18 0.04 240);
  --ring: oklch(0.65 0.2 240);
  --chart-1: oklch(0.55 0.25 270);
  --chart-2: oklch(0.6 0.18 180);
  --chart-3: oklch(0.65 0.2 60);
  --chart-4: oklch(0.5 0.22 300);
  --chart-5: oklch(0.58 0.24 30);
  --sidebar: oklch(0.05 0.04 240); /* Sidebar muito escura */
  --sidebar-foreground: oklch(0.95 0.01 240);
  --sidebar-primary: oklch(0.65 0.2 240);
  --sidebar-primary-foreground: oklch(0.08 0.06 240);
  --sidebar-accent: oklch(0.15 0.03 240);
  --sidebar-accent-foreground: oklch(0.9 0.01 240);
  --sidebar-border: oklch(0.18 0.03 240);
  --sidebar-ring: oklch(0.65 0.2 240);
}

@theme inline {
  /* optional: --font-sans, --font-serif, --font-mono if they are applied in the layout.tsx */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Gradientes adaptativos para modo claro e escuro */
  .gradient-hero {
    background: linear-gradient(135deg, 
      oklch(0.95 0.02 240) 0%, 
      oklch(0.9 0.04 240) 50%, 
      oklch(0.85 0.06 240) 100%);
  }
  
  .dark .gradient-hero {
    background: linear-gradient(135deg, 
      oklch(0.08 0.06 240) 0%, 
      oklch(0.15 0.08 240) 50%, 
      oklch(0.25 0.05 240) 100%);
  }

  .gradient-primary {
    background: linear-gradient(135deg, 
      oklch(0.55 0.18 240) 0%, 
      oklch(0.6 0.2 240) 100%);
  }
  
  .dark .gradient-primary {
    background: linear-gradient(135deg, 
      oklch(0.6 0.18 240) 0%, 
      oklch(0.7 0.2 240) 100%);
  }
  
  .gradient-sidebar {
    background: linear-gradient(180deg, 
      oklch(0.96 0.02 240) 0%, 
      oklch(0.94 0.03 240) 100%);
  }
  
  .dark .gradient-sidebar {
    background: linear-gradient(180deg, 
      oklch(0.05 0.04 240) 0%, 
      oklch(0.08 0.05 240) 100%);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-md {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  .text-shadow-lg {
    text-shadow: 0 15px 30px rgba(0, 0, 0, 0.11), 0 5px 15px rgba(0, 0, 0, 0.08);
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  .animate-pulse-subtle {
    animation: pulseSubtle 2s ease-in-out infinite;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulseSubtle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Landing Page Styles */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Estilização customizada para react-big-calendar */
.rbc-calendar {
  background: var(--background);
  color: var(--foreground);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}
.rbc-toolbar {
  background: var(--sidebar);
  color: var(--sidebar-foreground);
  border-bottom: 1px solid var(--sidebar-border);
}
.dark .rbc-toolbar {
  background: var(--sidebar);
  color: var(--sidebar-foreground);
}
.rbc-toolbar button {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border-radius: var(--radius-sm);
  border: none;
  margin-right: 4px;
  padding: 4px 10px;
  font-weight: 500;
  transition: background 0.2s;
}
.rbc-toolbar button.rbc-active {
  background: var(--primary);
  color: var(--primary-foreground);
}
.rbc-toolbar button:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}
.rbc-month-view, .rbc-time-view, .rbc-agenda-view {
  background: var(--card);
  color: var(--card-foreground);
  border-radius: var(--radius-md);
}
.rbc-header {
  background: var(--muted);
  color: var(--muted-foreground);
  font-weight: 600;
  border-bottom: 1px solid var(--border);
}
.rbc-today {
  background: var(--primary);
  color: var(--primary-foreground);
}
.rbc-event {
  background: var(--accent);
  color: var(--accent-foreground);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  font-weight: 500;
  box-shadow: 0 1px 4px #0001;
}
.rbc-event:hover {
  background: var(--primary);
  color: var(--primary-foreground);
}
.rbc-agenda-table {
  background: var(--card);
  color: var(--card-foreground);
}
.rbc-agenda-date-cell {
  color: var(--primary);
  font-weight: 600;
}
.rbc-agenda-time-cell {
  color: var(--muted-foreground);
}
.rbc-agenda-event-cell {
  color: var(--foreground);
}
.rbc-row-segment {
  border-radius: var(--radius-sm);
}
.rbc-off-range-bg {
  background: var(--muted);
}
.dark .rbc-calendar, .dark .rbc-month-view, .dark .rbc-time-view, .dark .rbc-agenda-view {
  background: var(--background);
  color: var(--foreground);
}
.dark .rbc-header {
  background: var(--muted);
  color: var(--muted-foreground);
}
.dark .rbc-event {
  background: var(--accent);
  color: var(--accent-foreground);
}
.dark .rbc-event:hover {
  background: var(--primary);
  color: var(--primary-foreground);
}
